import { SinonStub, stub, restore } from "sinon";
import { expect, should, use } from "chai";
import { factory } from "factory-girl";
import { complexStructure, createComplexStructure, truncate } from "../entities/helper";
import { FACTORY } from "../factories/common";
import { DomainMonitoringJob } from "../../skywind/jobs/domainMonitoringJob";
import { DomainStatus } from "../../skywind/services/domainWatcher/types";
import * as EntityService from "../../skywind/services/entity";
import { EntityStatus } from "../../skywind/entities/entity";
import * as httpClientModule from "../../skywind/utils/httpClient";
import { Models } from "../../skywind/models/models";
import config from "../../skywind/config";
import { TapkingMonitoredDomain } from "../../skywind/services/domainWatcher/tapkingAdapter";

should();
use(require("chai-as-promised"));

class MockHttpClient {
    private domains = new Set<string>();
    private domainStatuses = new Map<string, DomainStatus>();

    async get<T>(url: string): Promise<T> {
        if (url === "") {
            // List all domains
            return Array.from(this.domains).map(domain => this.mapTo(domain)) as T;
        } else {
            // Get specific domain
            const domain = decodeURIComponent(url.replace("/", ""));
            if (!this.domains.has(domain)) {
                throw new Error(`Domain ${domain} not found`);
            }
            return this.mapTo(domain) as T;
        }
    }

    async post<T>(_url: string, data: { domain: string }): Promise<T> {
        this.domains.add(data.domain);
        return this.mapTo(data.domain) as T;
    }

    async delete(url: string): Promise<void> {
        const domain = decodeURIComponent(url.replace("/", ""));
        this.domains.delete(domain);
    }

    private mapTo(domain: string): TapkingMonitoredDomain {
        return {
            name: domain,
            statuses: [{ accessStatus: this.domainStatuses.get(domain)?.accessStatus ?? "UNKNOWN" }]
        };
    }

    // Helper methods for testing
    setDomainStatus(domain: string, status: DomainStatus) {
        this.domainStatuses.set(domain, status);
    }

    getDomains(): string[] {
        return Array.from(this.domains);
    }

    clear() {
        this.domains.clear();
        this.domainStatuses.clear();
    }
}

describe("Domain Monitoring Job", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let masterEntity: any;
    let loggingOutput: any;
    let tapkingAdapter: any;

    before(async () => {
        loggingOutput = config.loggingOutput;
        config.loggingOutput = "console";
        tapkingAdapter = config.domainMonitoring.adapters.tapking;
        config.domainMonitoring.adapters.tapking = {
            baseUrl: "http://localhost",
            token: "test-token",
            regions: ["US"]
        };
        await truncate();
        await createComplexStructure();

        masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
    });

    beforeEach(async () => {
        await truncate();
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        createHttpClientStub.returns(httpClient);
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    after(() => {
        config.loggingOutput = loggingOutput;
        config.domainMonitoring.adapters.tapking = tapkingAdapter;
    });

    it("should process domains from static domain pools", async () => {
        const staticDomain1 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static1.example.com"
        });
        const staticDomain2 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static2.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticDomain1.id,
            isActive: true
        });
        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticDomain2.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        const domains = httpClient.getDomains();
        expect(domains).to.include("static1.example.com");
        expect(domains).to.include("static2.example.com");
        expect(domains).to.have.length(2);
    });

    it("should process domains from dynamic domain pools", async () => {
        const dynamicDomain1 = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic1.example.com",
            environment: "test"
        });
        const dynamicDomain2 = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic2.example.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain1.id,
            isActive: true
        });
        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain2.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        const domains = httpClient.getDomains();
        expect(domains).to.include("dynamic1.example.com");
        expect(domains).to.include("dynamic2.example.com");
        expect(domains).to.have.length(2);
    });

    it("should handle blocked domains and call setBlocked on sources", async () => {
        const blockedDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "blocked.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: blockedDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        expect(httpClient.getDomains()).to.not.include("blocked.example.com");

        const job = new DomainMonitoringJob();
        await job.fire();
        expect(httpClient.getDomains()).to.include("blocked.example.com");

        httpClient.setDomainStatus("blocked.example.com", {
            accessStatus: "BLOCKED",
            lastCheckedAt: new Date().toISOString()
        });

        await job.fire();
        expect(httpClient.getDomains()).to.not.include("blocked.example.com");
    });

    it("should handle multiple domain pools with same adapter", async () => {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static.example.com"
        });
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic.example.com",
            environment: "test"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "static-pool",
            domainWatcherAdapterId: "tapking"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "dynamic-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: staticDomain.id,
            isActive: true
        });

        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        const domains = httpClient.getDomains();
        expect(domains).to.include("static.example.com");
        expect(domains).to.include("dynamic.example.com");
        expect(domains).to.have.length(2);
    });

    it("should handle empty domain list", async () => {
        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.have.length(0);
    });

    it("should handle domains with different statuses correctly", async () => {
        const availableDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "available.example.com"
        });
        const unknownDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "unknown.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-status-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: availableDomain.id,
            isActive: true
        });
        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: unknownDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        httpClient.setDomainStatus("available.example.com", {
            accessStatus: "AVAILABLE",
            lastCheckedAt: new Date().toISOString()
        });

        const job = new DomainMonitoringJob();
        await job.fire();

        const domains = httpClient.getDomains();
        expect(domains).to.include("available.example.com");
        expect(domains).to.include("unknown.example.com");
        expect(domains).to.have.length(2);
    });

    it("should reuse watcher instances for the same adapter", async () => {
        const domain1 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "domain1.example.com"
        });
        const domain2 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "domain2.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-reuse-pool",
            domainWatcherAdapterId: "tapking"
        });

        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: domain1.id,
            isActive: true
        });
        await Models.StaticDomainPoolItemModel.create({
            staticDomainPoolId: staticDomainPool.id,
            staticDomainId: domain2.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(createHttpClientStub.called).to.be.true;

        const domains = httpClient.getDomains();
        expect(domains).to.include("domain1.example.com");
        expect(domains).to.include("domain2.example.com");
        expect(domains).to.have.length(2);
    });
});
